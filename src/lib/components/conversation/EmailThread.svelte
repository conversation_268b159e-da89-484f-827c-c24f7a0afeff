<script lang="ts">
	import { createEventDispatcher, onMount, afterUpdate, tick } from 'svelte';
	import { formatMessageTime, formatMessageDate, truncateMessage } from '$lib/utils/messageFormatter';
	import { getInitials } from '$lib/utils/avatarGenerator';
	import {
		ChevronDownOutline,
		ChevronRightOutline,
		MailBoxOutline,
		ClockOutline,
		ReplyOutline,
		ReplyAllOutline,
		ForwardOutline,
		ArrowDownOutline,
		PenOutline,
		EnvelopeOutline

	} from 'flowbite-svelte-icons';
	import EmailCompositionModal from './EmailCompositionModal.svelte';

	// Email thread interface
	interface EmailMessage {
		id: number;
		threadId: string;
		sender: {
			name: string;
			email: string;
			avatar?: string;
		};
		subject: string;
		body: string;
		timestamp: string;
		isRead: boolean;
		isFromSelf: boolean;
		hasAttachments?: boolean;
		attachmentCount?: number;
		cc?: string[];
		bcc?: string[];
	}

	interface EmailThread {
		id: string;
		subject: string;
		participants: string[];
		messageCount: number;
		lastActivity: string;
		isExpanded: boolean;
		messages: EmailMessage[];
		hasUnread: boolean;
		isNew?: boolean;
	}

	// Props
	export let threads: EmailThread[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	// Reference to the scrollable container
	let scrollContainer: HTMLDivElement;

	// Sticky date header state
	let stickyDate: string = '';
	let showStickyHeader = false;
	let dateGroupElements: HTMLElement[] = [];

	// Modal composition state management
	interface CompositionData {
		action: 'reply' | 'replyAll' | 'forward' | 'compose';
		originalMessageId?: number;
		content: string;
		subject: string;
		to: string[];
		cc: string[];
		bcc: string[];
		originalMessage?: {
			sender: { name: string; email: string };
			timestamp: string;
			body: string;
		};
	}

	// Modal state
	let isCompositionModalOpen: boolean = false;
	let currentCompositionData: CompositionData | null = null;

	// Track which messages have their quoted content expanded
	let expandedQuotedContent: Record<number, boolean> = {};

	// Create a reactive function to format email body
	$: formatEmailBodyReactive = (body: string, messageId?: number) => {
		if (!body) return '';

		// Check if this message has quoted content expanded
		const showQuoted = messageId ? expandedQuotedContent[messageId] === true : false;

		if (showQuoted) {
			// Show full content including quoted parts
			return body.replace(/\n/g, '<br>');
		} else {
			// Hide quoted content (old messages in replies)
			const cleanedBody = hideQuotedContent(body);
			return cleanedBody.replace(/\n/g, '<br>');
		}
	};

	// Scroll to bottom button state
	let hasUserScrolledUp = false;
	let isNearBottom = true;
	$: showScrollToBottomButton = hasUserScrolledUp && !isNearBottom;

	// Mock data for initial development - Insurance company customer conversation
	const mockThreads: EmailThread[] = [
		// TODAY - Auto Insurance Claim Thread
		{
			id: 'thread-1',
			subject: 'Auto Insurance Claim #AI-2024-089456 - Vehicle Damage Assessment',
			participants: ['Sarah Mitchell', 'Claims Department', 'Adjusters Team'],
			messageCount: 7,
			lastActivity: '2025-09-27T14:31:00Z',
			isExpanded: false,
			hasUnread: true,
			isNew: true,
			messages: [
				{
					id: 1,
					threadId: 'thread-1',
					sender: {
						name: 'Sarah Mitchell',
						email: '<EMAIL>',
					},
					subject: 'Auto Insurance Claim Submission - Policy #AL-*********',
					body: 'Dear SecureGuard Insurance,\n\nI am writing to file a claim for vehicle damage that occurred on September 23rd, 2025. I was involved in a collision at the intersection of Main Street and Oak Avenue in Springfield.\n\nPolicy Details:\n- Policy Number: AL-*********\n- Vehicle: 2022 Honda Accord, License Plate: ABC-1234\n- Date of Loss: September 23, 2025, approximately 2:30 PM\n\nIncident Description:\nI was proceeding through a green light when another vehicle ran the red light and struck the passenger side of my vehicle. The other driver has admitted fault and their insurance information is:\n- Driver: Michael Johnson\n- Insurance: RoadSafe Insurance\n- Policy: RS-445789\n\nDamage Assessment:\n- Significant damage to passenger side doors and rear quarter panel\n- Airbags deployed\n- Vehicle is currently undrivable and has been towed to Miller\'s Auto Body Shop\n\nI have attached photos of the damage, the police report (#SPD-2025-4567), and the other driver\'s insurance information.\n\nPlease advise on next steps and assign a claims adjuster to my case.\n\nThank you for your prompt attention to this matter.\n\nBest regards,\nSarah Mitchell\nPhone: (*************\nEmail: <EMAIL>',
					timestamp: '2025-09-25T09:15:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 4,
					cc: [],
					bcc: []
				},
				{
					id: 2,
					threadId: 'thread-1',
					sender: {
						name: 'Claims Department',
						email: '<EMAIL>'
					},
					subject: 'Re: Auto Insurance Claim Submission - Claim #AI-2024-089456 Opened',
					body: 'Dear Ms. Mitchell,\n\nThank you for promptly reporting your claim. We have received your submission and opened claim #AI-2024-089456 for the incident that occurred on September 23rd, 2025.\n\nClaim Details:\n- Claim Number: AI-2024-089456\n- Policy Number: AL-*********\n- Date of Loss: September 23, 2025\n- Estimated Processing Time: 5-7 business days\n\nNext Steps:\n1. We have assigned Senior Claims Adjuster Jennifer Rodriguez to your case\n2. Jennifer will contact you within 24 hours to schedule a vehicle inspection\n3. We will coordinate with RoadSafe Insurance regarding the other party\'s liability\n4. A rental car authorization will be processed within 2 business days\n\nRequired Documentation (Please provide if not already submitted):\n✓ Photos of damage (received)\n✓ Police report (received)\n✓ Other driver\'s insurance information (received)\n- Medical documentation (if applicable)\n- Repair estimates (will be obtained during inspection)\n\nYour deductible for collision coverage is $500 as per your policy terms. Based on the other driver\'s admission of fault, we anticipate full recovery from their insurance carrier.\n\nFor immediate assistance, please reference claim #AI-2024-089456 when calling our 24/7 claims hotline at 1-800-SECURE-1.\n\nWe understand this is a stressful time and are committed to resolving your claim efficiently and fairly.\n\nBest regards,\nSecureGuard Insurance Claims Department\nDirect Line: (555) 987-6543\nEmail: <EMAIL>',
					timestamp: '2025-09-25T14:22:00Z',
					isRead: true,
					isFromSelf: true,
					cc: ['<EMAIL>'],
					bcc: ['<EMAIL>']
				},
				{
					id: 3,
					threadId: 'thread-1',
					sender: {
						name: 'Jennifer Rodriguez',
						email: '<EMAIL>',
					},
					subject: 'Re: Auto Insurance Claim #AI-2024-089456 - Inspection Scheduling',
					body: 'Dear Ms. Mitchell,\n\nI am Jennifer Rodriguez, your assigned Senior Claims Adjuster for claim #AI-2024-089456. I have reviewed your claim submission and the supporting documentation you provided.\n\nInspection Scheduling:\nI would like to schedule an inspection of your vehicle at Miller\'s Auto Body Shop. I have the following availability:\n- Tuesday, September 26th: 10:00 AM or 2:00 PM\n- Wednesday, September 27th: 9:00 AM or 1:00 PM\n- Thursday, September 28th: 11:00 AM or 3:00 PM\n\nThe inspection typically takes 45-60 minutes and I will need you present to discuss the incident details.\n\nRental Car Authorization:\nI have expedited your rental car authorization. You can pick up a comparable vehicle (mid-size sedan) from Enterprise Rent-A-Car at 123 Commerce Drive. Your authorization number is RNT-456789. Please bring your driver\'s license and a credit card for incidentals.\n\nLiability Assessment:\nBased on the police report and the other driver\'s admission of fault, this appears to be a clear liability case. I have already initiated contact with RoadSafe Insurance to coordinate the claim. This should result in full recovery of your deductible once their investigation is complete.\n\nPreliminary Damage Assessment:\nFrom the photos provided, I can see significant structural damage that will likely require:\n- Passenger side door replacement\n- Quarter panel repair/replacement\n- Airbag system replacement\n- Potential frame alignment\n\nEstimated repair time: 2-3 weeks once parts are ordered.\n\nPlease reply with your preferred inspection time, and I will confirm the appointment.\n\nBest regards,\nJennifer Rodriguez\nSenior Claims Adjuster\nSecureGuard Insurance\nDirect: (*************\nMobile: (*************\nEmail: <EMAIL>',
					timestamp: '2025-09-25T16:45:00Z',
					isRead: true,
					isFromSelf: true,
					hasAttachments: false,
					cc: ['<EMAIL>'],
					bcc: []
				},
				{
					id: 4,
					threadId: 'thread-1',
					sender: {
						name: 'Sarah Mitchell',
						email: '<EMAIL>',
					},
					subject: 'Re: Auto Insurance Claim #AI-2024-089456 - Inspection Confirmation',
					body: 'Dear Jennifer,\n\nThank you for the quick response and for expediting the rental car authorization. I really appreciate the excellent service so far.\n\nInspection Scheduling:\nWednesday, September 27th at 1:00 PM works perfectly for me. I will meet you at Miller\'s Auto Body Shop (456 Industrial Blvd, Springfield).\n\nRental Car Update:\nI picked up the rental car yesterday - a 2024 Nissan Altima. The Enterprise staff was very helpful and the authorization process was seamless.\n\nAdditional Information:\nI wanted to provide some additional details that might be helpful:\n- I have been experiencing some minor neck stiffness since the accident\n- My regular mechanic mentioned the vehicle may have had previous minor damage to the rear bumper (unrelated to this incident)\n- I have maintenance records showing the vehicle was in excellent condition prior to the accident\n\nQuestions:\n1. Should I see a doctor for the neck stiffness, and would this be covered under my policy?\n2. Will the previous minor bumper damage affect my claim?\n3. What happens if Miller\'s Auto Body Shop finds additional damage during teardown?\n\nI have also attached the maintenance records and a photo of the previous bumper damage for your reference.\n\nLooking forward to meeting with you on Wednesday.\n\nBest regards,\nSarah Mitchell\nPhone: (*************',
					timestamp: '2025-09-26T10:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 2,
					cc: [],
					bcc: []
				},
				{
					id: 5,
					threadId: 'thread-1',
					sender: {
						name: 'Jennifer Rodriguez',
						email: '<EMAIL>'
					},
					subject: 'Re: Auto Insurance Claim #AI-2024-089456 - Medical Coverage & Inspection Confirmed',
					body: 'Dear Ms. Mitchell,\n\nThank you for the additional information and documentation. I have confirmed our appointment for Wednesday, September 27th at 1:00 PM at Miller\'s Auto Body Shop.\n\nAnswers to Your Questions:\n\n1. Medical Coverage:\nYes, please see a doctor for your neck stiffness. Your policy includes Personal Injury Protection (PIP) with $10,000 coverage for medical expenses. I recommend visiting an urgent care center or your primary physician. Keep all receipts and medical documentation. No pre-authorization is required for initial treatment.\n\n2. Previous Damage:\nThe minor bumper damage you disclosed will not affect your current claim since it\'s unrelated to the accident area (passenger side). I appreciate your transparency in reporting this. We will note it in the file to avoid any confusion during the repair process.\n\n3. Additional Damage Discovery:\nIf Miller\'s Auto Body discovers additional damage during teardown (which is common), they will document it with photos and provide a supplemental estimate. I will review and approve covered damages promptly to avoid repair delays.\n\nClaim Status Update:\n- RoadSafe Insurance has accepted liability for their insured\n- Your $500 deductible will be waived due to the other party\'s fault\n- Total loss threshold for your vehicle is $18,500 (75% of actual cash value)\n- Current damage estimate: $12,000-15,000 (repair recommended)\n\nNext Steps After Inspection:\n1. I will provide a detailed damage assessment\n2. Authorize repairs with Miller\'s Auto Body Shop\n3. Coordinate with RoadSafe for subrogation recovery\n4. Process any medical claims under your PIP coverage\n\nPlease bring your driver\'s license and policy documents to our meeting.\n\nBest regards,\nJennifer Rodriguez\nSenior Claims Adjuster\nSecureGuard Insurance\nDirect: (*************',
					timestamp: '2025-09-26T13:15:00Z',
					isRead: true,
					isFromSelf: true,
					cc: ['<EMAIL>'],
					bcc: []
				},
				{
					id: 6,
					threadId: 'thread-1',
					sender: {
						name: 'Sarah Mitchell',
						email: '<EMAIL>',
					},
					subject: 'Re: Auto Insurance Claim #AI-2024-089456 - Post-Inspection Update',
					body: 'Dear Jennifer,\n\nThank you for the thorough inspection yesterday. I was impressed with your professionalism and attention to detail during the assessment.\n\nInspection Follow-up:\nAs discussed, Miller\'s Auto Body Shop found additional damage to the frame during their detailed inspection. The supplemental estimate of $3,200 brings the total repair cost to $15,800. I understand you\'ve already approved this additional work.\n\nMedical Update:\nI visited Dr. Sarah Chen at Springfield Medical Center yesterday. She diagnosed me with minor cervical strain and recommended physical therapy. I have attached the medical report and treatment plan. The total estimated medical costs are $1,200 over 6 weeks.\n\nRepair Timeline:\nMiller\'s Auto Body estimates 18 business days for completion once all parts arrive. They mentioned that the passenger door has a 2-week lead time from Honda.\n\nQuestions:\n1. Can I extend my rental car coverage for the full repair period?\n2. Will SecureGuard coordinate directly with Dr. Chen\'s office for the medical billing?\n3. Should I expect any communication from RoadSafe Insurance directly?\n\nI wanted to express my gratitude for the exceptional service throughout this process. This is exactly the kind of support I hoped for when I chose SecureGuard Insurance.\n\nPlease let me know if you need any additional information.\n\nBest regards,\nSarah Mitchell\n\nP.S. - I\'ve referred my neighbor to SecureGuard based on this positive experience.\n\nOn 21:31, Jennifer Rodriguez wrote:\nDear Ms. Mitchell,\n\nThank you for the additional information and documentation. I have confirmed our appointment for Wednesday, September 27th at 1:00 PM at Miller\'s Auto Body Shop.\n\nAnswers to Your Questions:\n\n1. Medical Coverage:\nYes, please see a doctor for your neck stiffness. Your policy includes Personal Injury Protection (PIP) with $10,000 coverage for medical expenses. I recommend visiting an urgent care center or your primary physician. Keep all receipts and medical documentation. No pre-authorization is required for initial treatment.',
					timestamp: '2025-09-27T14:31:00Z',
					isRead: false,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 3,
					cc: [],
					bcc: []
				},
				{
					id: 7,
					threadId: 'thread-1',
					sender: {
						name: 'Analytics Team',
						email: '<EMAIL>'
					},
					subject: 'Re: Auto Insurance Claim #AI-2024-089456 - Post-Inspection Update',
					body: 'Thank you\n\nOn 14:31, Sarah Mitchell wrote:\n\nsomething',
					timestamp: '2025-09-27T15:00:00Z',
					isRead: false,
					isFromSelf: true,
					hasAttachments: false,
					cc: [],
					bcc: []
				}
			]
		},
		// SEPTEMBER 25TH - Project Collaboration Thread with Sarah Mitchell
		{
			id: 'thread-sarah-1',
			subject: 'Q4 Marketing Campaign Strategy - Creative Assets Review',
			participants: ['Sarah Mitchell', 'Marketing Team', 'Creative Director', 'You'],
			messageCount: 4,
			lastActivity: '2025-09-25T16:45:00Z',
			isExpanded: false,
			hasUnread: false,
			messages: [
				{
					id: 101,
					threadId: 'thread-sarah-1',
					sender: {
						name: 'Sarah Mitchell',
						email: '<EMAIL>',
					},
					subject: 'Q4 Marketing Campaign Strategy - Creative Assets Review',
					body: 'Hi team,\n\nI hope everyone is doing well! I wanted to share the initial creative concepts for our Q4 marketing campaign and get your feedback.\n\nCampaign Overview:\n- Theme: "Innovation Meets Reliability"\n- Target Audience: Tech-savvy professionals aged 25-45\n- Key Channels: Digital advertising, social media, email marketing\n- Budget: $150,000 allocated across all channels\n\nCreative Assets Included:\n• 3 hero banner designs for website homepage\n• Social media template pack (Instagram, LinkedIn, Twitter)\n• Email newsletter templates\n• Video storyboard concepts\n\nI\'ve attached the creative brief and initial mockups for your review. Please take a look and let me know your thoughts by Friday so we can incorporate feedback before our client presentation next week.\n\nSpecific areas I\'d love input on:\n1. Do the visuals align with our brand guidelines?\n2. Is the messaging compelling for our target audience?\n3. Any concerns about the proposed timeline?\n\nLooking forward to your insights!\n\nBest regards,\nSarah Mitchell\nSenior Marketing Strategist\nPhone: (*************',
					timestamp: '2025-09-25T09:30:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: true,
					attachmentCount: 5,
					cc: ['<EMAIL>'],
					bcc: []
				},
				{
					id: 102,
					threadId: 'thread-sarah-1',
					sender: {
						name: 'Creative Director',
						email: '<EMAIL>'
					},
					subject: 'Re: Q4 Marketing Campaign Strategy - Creative Assets Review',
					body: 'Sarah,\n\nExcellent work on the creative brief! The concepts are strong and definitely align with our brand direction.\n\nFeedback on the assets:\n\n✓ Hero banners: Love the clean, modern aesthetic. The color palette works perfectly with our brand guidelines.\n✓ Social media templates: Great variety and very engaging. The Instagram stories template is particularly strong.\n✓ Email templates: Clean design, good use of white space. Mobile responsiveness looks solid.\n\nSuggestions:\n• Consider adding a subtle animation element to the hero banners for web\n• The LinkedIn template could use slightly more professional tone in the copy\n• Video storyboard concept #2 resonates most with our target demographic\n\nTimeline looks realistic. I can have the final assets ready by Monday if we finalize feedback by Friday as planned.\n\nOne question: Do we have approval for the increased video production budget mentioned in the brief?\n\nGreat job leading this initiative!\n\nBest,\nMark Stevens\nCreative Director',
					timestamp: '2025-09-25T11:15:00Z',
					isRead: true,
					isFromSelf: true,
					hasAttachments: false,
					cc: ['<EMAIL>'],
					bcc: []
				},
				{
					id: 103,
					threadId: 'thread-sarah-1',
					sender: {
						name: 'You',
						email: '<EMAIL>'
					},
					subject: 'Re: Q4 Marketing Campaign Strategy - Creative Assets Review',
					body: 'Sarah and Mark,\n\nImpressive work on this campaign! The strategic approach is solid and the creative execution looks promising.\n\nFrom a business perspective:\n• The target audience segmentation is spot-on for our Q4 goals\n• Budget allocation seems reasonable, especially the 40% digital focus\n• Timeline aligns well with our product launch schedule\n\nRegarding Mark\'s question about video production budget - yes, we have approval for the additional $25K for video content. This was cleared in last week\'s budget review meeting.\n\nAdditional thoughts:\n• Consider A/B testing the email subject lines before full deployment\n• The social media calendar should account for holiday posting schedules\n• We should coordinate with the PR team for potential media tie-ins\n\nI\'ll set up a follow-up meeting for next Tuesday to finalize everything before the client presentation.\n\nExcellent collaboration, team!\n\nBest,\n[Your Name]',
					timestamp: '2025-09-25T14:20:00Z',
					isRead: true,
					isFromSelf: true,
					hasAttachments: false,
					cc: [],
					bcc: []
				},
				{
					id: 104,
					threadId: 'thread-sarah-1',
					sender: {
						name: 'Sarah Mitchell',
						email: '<EMAIL>',
					},
					subject: 'Re: Q4 Marketing Campaign Strategy - Final Updates',
					body: 'Hi everyone,\n\nThank you so much for the thoughtful feedback! I\'ve incorporated all the suggestions and the campaign is looking even stronger.\n\nUpdates made:\n✓ Added subtle hover animations to hero banners (Mark\'s suggestion)\n✓ Refined LinkedIn template copy for more professional tone\n✓ Selected storyboard concept #2 for video production\n✓ Scheduled A/B testing for email subject lines\n✓ Coordinated with PR team for media integration opportunities\n\nNext Steps:\n• Mark: Final asset delivery by Monday ✓ Confirmed\n• Video production: Starting Tuesday with approved budget\n• A/B testing: Will run for 48 hours before full campaign launch\n• Client presentation: Wednesday at 2 PM (calendar invite sent)\n\nI\'m really excited about how this campaign is shaping up! The collaborative effort from everyone has made this so much stronger than my initial concept.\n\nOne small request: Could someone from the analytics team join our Tuesday meeting to discuss tracking and KPI measurement?\n\nThanks again for making this such a smooth process!\n\nBest,\nSarah\n\nP.S. - I\'ve booked the conference room with the big screen for our client presentation. Coffee and pastries will be provided! ☕',
					timestamp: '2025-09-25T16:45:00Z',
					isRead: true,
					isFromSelf: false,
					hasAttachments: false,
					cc: ['<EMAIL>'],
					bcc: []
				}
			]
		},
	];

	// Initialize with mock data if no threads provided
	if (threads.length === 0) {
		threads = mockThreads;
	}

	// Track if we've done the initial scroll
	let hasInitialScrolled = false;

	// Toggle thread expansion
	function toggleThread(threadId: string) {
		threads = threads.map(thread => {
			if (thread.id === threadId) {
				return { ...thread, isExpanded: !thread.isExpanded };
			}
			return thread;
		});

		dispatch('threadToggle', { threadId, isExpanded: threads.find(t => t.id === threadId)?.isExpanded });
	}

	// Handle email click
	function handleEmailClick(email: EmailMessage) {
		dispatch('emailClick', { email });
	}

	// Handle email actions
	function handleEmailAction(action: string, email: EmailMessage, event?: Event) {
		if (event) {
			event.stopPropagation();
		}

		// Handle modal composition actions
		if (action === 'reply' || action === 'replyAll' || action === 'forward') {
			startModalComposition(action as 'reply' | 'replyAll' | 'forward', email);
		} else {
			// Dispatch other actions as before
			dispatch('emailAction', { action, email });
		}
	}

	// Start modal composition
	function startModalComposition(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage) {
		currentCompositionData = {
			action,
			originalMessageId: email.id,
			content: getInitialContent(action, email),
			subject: getSubjectForAction(action, email.subject),
			to: getRecipientsForAction(action, email),
			cc: getCCRecipientsForAction(action, email),
			bcc: getBCCRecipientsForAction(action, email),
			originalMessage: {
				sender: email.sender,
				timestamp: email.timestamp,
				body: email.body
			}
		};

		isCompositionModalOpen = true;
	}

	// Get initial content based on action
	function getInitialContent(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string {
		const timestamp = formatMessageTime(email.timestamp);
		const sender = email.sender.name;

		if (action === 'forward') {
			return `<br><br>---------- Forwarded message ----------<br>From: ${sender} &lt;${email.sender.email}&gt;<br>Date: ${timestamp}<br>Subject: ${email.subject}<br><br>${email.body}`;
		} else {
			return `<br><br>On ${timestamp}, ${sender} wrote:<br><blockquote style="margin-left: 20px; border-left: 2px solid #ccc; padding-left: 10px;">${email.body}</blockquote>`;
		}
	}

	// Get subject for action
	function getSubjectForAction(action: 'reply' | 'replyAll' | 'forward', originalSubject: string): string {
		if (action === 'forward') {
			return originalSubject.startsWith('Fwd:') ? originalSubject : `Fwd: ${originalSubject}`;
		} else {
			return originalSubject.startsWith('Re:') ? originalSubject : `Re: ${originalSubject}`;
		}
	}

	// Get recipients for action
	function getRecipientsForAction(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string[] {
		if (action === 'forward') {
			return []; // User will fill in recipients
		} else if (action === 'replyAll') {
			// For Reply All: Include original sender and infer other "To" recipients from thread
			const recipients = new Set<string>();

			// Always include the original sender (unless it's from current user)
			if (!email.isFromSelf) {
				recipients.add(email.sender.email);
			}

			// Find other participants who should be in "To" field by looking at thread participants
			// and excluding those who are likely Cc recipients
			const thread = threads.find(t => t.messages.some(m => m.id === email.id));
			if (thread) {
				// Convert thread participant names to email addresses
				// This is a simplified mapping - in a real app, you'd have a proper participant-to-email mapping
				const participantEmailMap: { [key: string]: string } = {
					'Samuel Jennings': '<EMAIL>',
					'IT Team': '<EMAIL>',
					'Finance Team': '<EMAIL>',
					'Sarah Chen': '<EMAIL>',
					'Mike Rodriguez': '<EMAIL>',
					'Sarah Mitchell': '<EMAIL>',
					'Marketing Team': '<EMAIL>',
					'Creative Director': '<EMAIL>',
					'HR Department': '<EMAIL>',
					'Account Management': '<EMAIL>',
					'IT Support': '<EMAIL>',
					'Marketing Operations': '<EMAIL>',
					'You': '<EMAIL>'
				};

				thread.participants.forEach(participant => {
					const email_addr = participantEmailMap[participant];
					if (email_addr && email_addr !== '<EMAIL>') {
						recipients.add(email_addr);
					}
				});
			}

			return Array.from(recipients);
		} else {
			// Reply only to sender
			return [email.sender.email];
		}
	}

	// Get CC recipients for action
	function getCCRecipientsForAction(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string[] {
		if (action === 'replyAll') {
			// For reply all, include original CC recipients (excluding current user)
			const originalCC = email.cc || [];
			return originalCC.filter(cc => cc !== '<EMAIL>');
		} else if (action === 'forward') {
			// For forward, start with empty CC (user will add as needed)
			return [];
		} else {
			// For regular reply, don't include CC by default
			return [];
		}
	}

	// Get BCC recipients for action
	function getBCCRecipientsForAction(action: 'reply' | 'replyAll' | 'forward', email: EmailMessage): string[] {
		if (action === 'forward') {
			// For forward, preserve original BCC if forwarding internally
			// In practice, BCC is usually not preserved when forwarding
			return [];
		} else if (action === 'replyAll') {
			// For reply all, BCC is typically not preserved for privacy
			return [];
		} else {
			// For regular reply, BCC is empty
			return [];
		}
	}

	// Handle modal composition send
	function handleModalSend(event: CustomEvent) {
		// Dispatch the send event with composition data
		dispatch('emailSend', event.detail);

		// Close the modal
		isCompositionModalOpen = false;
		currentCompositionData = null;

		// Add the new email to mockThreads data structure
		addEmailToThreads(event.detail);
	}

	// Add email to threads and handle UI state management
	function addEmailToThreads(emailData: any) {
		const { action, originalMessageId, content, subject, to, cc, bcc } = emailData;

		// Create the new message object
		const newMessage: EmailMessage = {
			id: Date.now(),
			threadId: '', // Will be set based on action type
			sender: {
				name: 'You',
				email: '<EMAIL>'
			},
			subject: subject,
			body: content,
			timestamp: new Date().toISOString(),
			isRead: true,
			isFromSelf: true,
			hasAttachments: false,
			attachmentCount: 0,
			cc: cc || [],
			bcc: bcc || []
		};

		let targetThreadId: string;

		if (action === 'compose') {
			// New Compose: Create a new thread entry
			targetThreadId = `thread-${Date.now()}`;
			newMessage.threadId = targetThreadId;

			// Create new thread
			const newThread: EmailThread = {
				id: targetThreadId,
				subject: subject,
				participants: ['You', ...to],
				messageCount: 1,
				lastActivity: newMessage.timestamp,
				isExpanded: true, // Auto-expand new thread
				messages: [newMessage],
				hasUnread: false,
				isNew: true
			};

			// Add new thread to the beginning of mockThreads (most recent first)
			mockThreads.unshift(newThread);
		} else {
			// Reply: Add email as child message under existing parent thread
			if (originalMessageId) {
				// Find the thread containing the original message
				const originalThread = mockThreads.find(thread =>
					thread.messages.some(msg => msg.id === originalMessageId)
				);

				if (originalThread) {
					targetThreadId = originalThread.id;
					newMessage.threadId = targetThreadId;

					// Add message to existing thread
					originalThread.messages.push(newMessage);
					originalThread.messageCount = originalThread.messages.length;
					originalThread.lastActivity = newMessage.timestamp;
					originalThread.isExpanded = true; // Auto-expand modified thread
					originalThread.hasUnread = false; // Mark as read since user just sent
				} else {
					// Fallback: if original thread not found, add to first thread
					console.warn('Original thread not found, adding to first thread');
					targetThreadId = mockThreads[0].id;
					newMessage.threadId = targetThreadId;
					mockThreads[0].messages.push(newMessage);
					mockThreads[0].messageCount = mockThreads[0].messages.length;
					mockThreads[0].lastActivity = newMessage.timestamp;
					mockThreads[0].isExpanded = true;
				}
			} else {
				// Fallback: if no original message ID, add to first thread
				console.warn('No original message ID found, adding to first thread');
				targetThreadId = mockThreads[0].id;
				newMessage.threadId = targetThreadId;
				mockThreads[0].messages.push(newMessage);
				mockThreads[0].messageCount = mockThreads[0].messages.length;
				mockThreads[0].lastActivity = newMessage.timestamp;
				mockThreads[0].isExpanded = true;
			}
		}

		// Reset the threads to trigger reactivity
		threads = [...mockThreads];

		// Scroll to the newly added message after DOM updates
		setTimeout(() => {
			scrollToNewMessage(targetThreadId);
		}, 100);
	}

	// Scroll to the bottom of the expanded thread containing the new message
	function scrollToNewMessage(threadId: string) {
		if (!scrollContainer) return;

		// First, ensure the thread is expanded and DOM is updated
		tick().then(() => {
			// Find the thread element
			const threadElement = scrollContainer.querySelector(`[data-thread-id="${threadId}"]`);
			if (threadElement) {
				// Scroll the thread into view first
				threadElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

				// Then scroll to the bottom of the container to show the newest message
				setTimeout(() => {
					const scrollHeight = scrollContainer.scrollHeight;
					const clientHeight = scrollContainer.clientHeight;
					const targetScrollTop = scrollHeight - clientHeight;

					scrollContainer.scrollTo({
						top: targetScrollTop,
						behavior: 'smooth'
					});
				}, 200);
			} else {
				// Fallback: scroll to bottom of container
				scrollToLatestContent();
			}
		});
	}

	// Handle modal composition cancel/close
	function handleModalCancel() {
		isCompositionModalOpen = false;
		currentCompositionData = null;
	}

	// Start new email composition
	function startNewComposition() {
		currentCompositionData = {
			action: 'compose',
			content: '',
			subject: '',
			to: [],
			cc: [],
			bcc: []
		};

		isCompositionModalOpen = true;
	}

	// Group threads by exact date for display
	function groupThreadsByDate(threads: EmailThread[]) {
		const dateGroups = new Map<string, EmailThread[]>();

		threads.forEach(thread => {
			const threadDate = new Date(thread.lastActivity);
			// Use YYYY-MM-DD format for consistent grouping
			const dateKey = threadDate.toISOString().split('T')[0];

			if (!dateGroups.has(dateKey)) {
				dateGroups.set(dateKey, []);
			}
			dateGroups.get(dateKey)!.push(thread);
		});

		// Convert to array and sort by date (oldest first)
		const sortedGroups = Array.from(dateGroups.entries())
			.sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
			.map(([dateKey, threads]) => ({
				dateKey,
				date: new Date(dateKey),
				threads: threads.sort((a, b) =>
					new Date(a.lastActivity).getTime() - new Date(b.lastActivity).getTime()
				)
			}));

		return sortedGroups;
	}

	// Get avatar color for sender
	function getAvatarColor(name: string): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];

		let hash = 0;
		for (let i = 0; i < name.length; i++) {
			hash = name.charCodeAt(i) + ((hash << 5) - hash);
		}

		return colors[Math.abs(hash) % colors.length];
	}



	// Hide quoted content from email replies
	function hideQuotedContent(body: string): string {
		if (!body) return '';

		// Common patterns for quoted content in email replies
		const quotedPatterns = [
			// Simple and effective pattern for "On [time], [name] wrote:" - matches the exact format from screenshot
			/\n\s*On\s+\d{1,2}:\d{2},\s+[^:]+\s+wrote:[\s\S]*$/i,
			// More general "On [date/time], [name] wrote:" pattern
			/\n\s*On\s+[^,]+,\s+[^:]+\s+wrote:[\s\S]*$/i,
			// "On [date] at [time], [name] wrote:" pattern
			/\n\s*On\s+[^,]+\s+at\s+[^,]+,\s+[^:]+\s+wrote:[\s\S]*$/i,
			// HTML blockquote pattern (from our own reply composition)
			/<blockquote[^>]*>[\s\S]*?<\/blockquote>/gi,
			// "From: [email]" forwarded message pattern
			/\n\s*-+\s*Forwarded message\s*-+[\s\S]*$/i,
			// Email signature separators
			/\n\s*--\s*\n[\s\S]*$/,
			// Common reply separators
			/\n\s*_{5,}\s*\n[\s\S]*$/,
			/\n\s*={5,}\s*\n[\s\S]*$/
		];

		let cleanedBody = body;

		// Apply each pattern to remove quoted content
		for (const pattern of quotedPatterns) {
			cleanedBody = cleanedBody.replace(pattern, '');
		}

		// Trim any trailing whitespace
		return cleanedBody.trim();
	}

	// Check if a message has quoted content
	function hasQuotedContent(body: string): boolean {
		if (!body) return false;

		const quotedPatterns = [
			// Simple and effective pattern for "On [time], [name] wrote:" - matches the exact format from screenshot
			/\n\s*On\s+\d{1,2}:\d{2},\s+[^:]+\s+wrote:/i,
			// More general "On [date/time], [name] wrote:" pattern
			/\n\s*On\s+[^,]+,\s+[^:]+\s+wrote:/i,
			// "On [date] at [time], [name] wrote:" pattern
			/\n\s*On\s+[^,]+\s+at\s+[^,]+,\s+[^:]+\s+wrote:/i,
			// HTML blockquote pattern
			/<blockquote[^>]*>/i,
			// "From: [email]" forwarded message pattern
			/\n\s*-+\s*Forwarded message\s*-+/i,
			// Email signature separators
			/\n\s*--\s*\n/,
			/\n\s*_{5,}\s*\n/,
			/\n\s*={5,}\s*\n/
		];

		return quotedPatterns.some(pattern => pattern.test(body));
	}

	// Toggle quoted content visibility for a message
	function toggleQuotedContent(messageId: number) {
		if (expandedQuotedContent[messageId]) {
			delete expandedQuotedContent[messageId];
		} else {
			expandedQuotedContent[messageId] = true;
		}
		expandedQuotedContent = { ...expandedQuotedContent }; // Trigger reactivity
	}

	// Get relative time for last activity
	function getRelativeTime(timestamp: string): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffHours / 24);

		if (diffHours < 1) return 'เมื่อสักครู่';
		if (diffHours < 24) return `${diffHours} ชั่วโมงที่ผ่านมา`;
		if (diffDays === 1) return 'เมื่อวานนี้';
		if (diffDays < 7) return `${diffDays} วันที่ผ่านมา`;

		return formatMessageDate(timestamp);
	}

	// Function to scroll to the latest content (newest threads at bottom)
	function scrollToLatestContent() {
		console.log('scrollToLatestContent called, container exists:', !!scrollContainer);

		if (scrollContainer) {
			// Use multiple approaches to ensure scrolling works reliably
			const performScroll = () => {
				// Always scroll to bottom since latest emails are now at the bottom
				const scrollHeight = scrollContainer.scrollHeight;
				const clientHeight = scrollContainer.clientHeight;
				const currentScrollTop = scrollContainer.scrollTop;

				console.log('Scroll to bottom attempt:', {
					scrollHeight,
					clientHeight,
					currentScrollTop,
					needsScroll: scrollHeight > clientHeight,
					maxScrollTop: scrollHeight - clientHeight
				});

				if (scrollHeight > clientHeight) {
					// Try multiple scroll methods for reliability
					const targetScrollTop = scrollHeight - clientHeight;

					// Method 1: Direct scrollTop assignment
					scrollContainer.scrollTop = targetScrollTop;

					// Method 2: scrollTo for better browser support
					scrollContainer.scrollTo({
						top: targetScrollTop,
						behavior: 'smooth'
					});

					// Verify the scroll worked
					setTimeout(() => {
						const newScrollTop = scrollContainer.scrollTop;
						console.log('Scroll result:', {
							targetScrollTop,
							actualScrollTop: newScrollTop,
							scrollWorked: newScrollTop > currentScrollTop,
							isAtBottom: Math.abs(newScrollTop - targetScrollTop) < 5
						});

						// If smooth scroll didn't work, force instant scroll
						if (Math.abs(newScrollTop - targetScrollTop) > 5) {
							scrollContainer.scrollTop = targetScrollTop;
						}
					}, 200);
				} else {
					console.log('No scroll needed - content fits in container');
				}
			};

			// Use requestAnimationFrame to ensure DOM is ready
			requestAnimationFrame(() => {
				performScroll();
				// Try again after a short delay in case content is still loading
				setTimeout(performScroll, 100);
				// One more attempt with longer delay for slow-loading content
				setTimeout(performScroll, 300);
			});
		} else {
			console.log('Scroll container not available yet');
		}
	}

	// Auto-scroll to latest content when component mounts
	onMount(async () => {
		console.log('EmailThread component mounted');
		// Wait for DOM to be fully rendered
		await tick();
		// Try scrolling multiple times with different delays to ensure it works
		setTimeout(scrollToLatestContent, 0);
		setTimeout(scrollToLatestContent, 100);
		setTimeout(scrollToLatestContent, 300);
		setTimeout(scrollToLatestContent, 500);
	});

	// Also try scrolling after each update
	afterUpdate(() => {
		if (!hasInitialScrolled && scrollContainer && threads.length > 0) {
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 50);
		}
	});

	// Reactive statement to group threads by date
	$: groupedThreads = groupThreadsByDate(threads);

	// Create date groups with formatted labels
	$: dateGroups = groupedThreads.map(group => ({
		label: formatMessageDate(group.date.toISOString()),
		threads: group.threads,
		dateKey: group.dateKey
	}));

	// Ensure dateGroupElements array is properly sized
	$: if (dateGroups.length !== dateGroupElements.length) {
		const newArray = new Array(dateGroups.length);
		// Copy existing references to the new array where possible
		for (let i = 0; i < Math.min(dateGroupElements.length, newArray.length); i++) {
			newArray[i] = dateGroupElements[i];
		}
		dateGroupElements = newArray;
	}

	// Auto-scroll to bottom when threads change (new data loaded) or on initial load
	$: if (threads.length > 0 && scrollContainer && !hasInitialScrolled) {
		// Reset scroll state for new content
		hasUserScrolledUp = false;
		isNearBottom = true;

		// Use tick to ensure DOM is updated before scrolling
		tick().then(() => {
			// Add a small delay to ensure content is fully rendered
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 100);
		});
	}

	// Also scroll when grouped threads change (after content is rendered)
	$: if (groupedThreads && scrollContainer && !hasInitialScrolled) {
		// Reset scroll state for new content
		hasUserScrolledUp = false;
		isNearBottom = true;

		tick().then(() => {
			setTimeout(() => {
				scrollToLatestContent();
				hasInitialScrolled = true;
			}, 150);
		});
	}

	// Update sticky header after DOM updates
	afterUpdate(() => {
		updateStickyHeader();
	});

	// Wait for content to be fully rendered and sized
	async function waitForContentStability(): Promise<void> {
		if (!scrollContainer) return;

		// Wait for DOM updates to complete using multiple animation frames
		await new Promise<void>(resolve => {
			requestAnimationFrame(() => {
				requestAnimationFrame(() => {
					requestAnimationFrame(() => {
						resolve();
					});
				});
			});
		});

		// Wait for layout calculations to complete
		await new Promise<void>(resolve => setTimeout(resolve, 50));

		// Ensure scroll height has stabilized
		let previousScrollHeight = scrollContainer.scrollHeight;
		await new Promise<void>(resolve => {
			const checkStability = () => {
				const currentScrollHeight = scrollContainer.scrollHeight;
				if (currentScrollHeight === previousScrollHeight) {
					resolve();
				} else {
					previousScrollHeight = currentScrollHeight;
					setTimeout(checkStability, 25);
				}
			};
			setTimeout(checkStability, 25);
		});
	}

	// Check if user is at the bottom of the scroll container
	function isAtBottom(tolerance: number = 10): boolean {
		if (!scrollContainer) return false;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Method 1: Standard calculation with tolerance
		const method1 = distanceFromBottom <= tolerance;

		// Method 2: Direct position check
		const method2 = scrollTop >= (scrollHeight - clientHeight - tolerance);

		// Method 3: Percentage-based check (within 99% of total scroll)
		const scrollPercentage = scrollTop / Math.max(1, scrollHeight - clientHeight);
		const method3 = scrollPercentage >= 0.99;

		// Return true if any method confirms we're at bottom
		return method1 || method2 || method3;
	}

	// Handle scroll events to update sticky header and track scroll position
	function handleScroll() {
		if (!scrollContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px) - same threshold as MessageList
		const wasNearBottom = isNearBottom;
		isNearBottom = distanceFromBottom < 100;

		// Also check if we're truly at the bottom for more accurate detection
		const atBottom = isAtBottom();

		// Track manual scrolling: user has scrolled up if they moved away from bottom
		// Only set hasUserScrolledUp when user manually scrolls up (not when auto-scrolling)
		if (!wasNearBottom && !isNearBottom) {
			// User is scrolling while already away from bottom - maintain hasUserScrolledUp state
		} else if (wasNearBottom && !isNearBottom) {
			// User just scrolled up from being near bottom - this is a manual scroll up
			hasUserScrolledUp = true;
		} else if (!wasNearBottom && isNearBottom) {
			// User scrolled back to near bottom - reset the flag
			hasUserScrolledUp = false;
		} else if (atBottom) {
			// User is at the very bottom - definitely reset the flag
			hasUserScrolledUp = false;
		}

		updateStickyHeader();
	}

	// Handle scroll to bottom button click with robust bottom detection
	async function handleScrollToBottomClick() {
		if (!scrollContainer) return;

		hasUserScrolledUp = false; // Reset when user manually scrolls to bottom

		// Wait for content to be fully rendered and stable
		await waitForContentStability();

		// Use multiple attempts to ensure we reach the true bottom
		const attemptScroll = (attempt = 0) => {
			const { scrollHeight, clientHeight } = scrollContainer;
			const targetScrollTop = Math.max(0, scrollHeight - clientHeight);

			// Smooth scroll to the calculated bottom position
			scrollContainer.scrollTo({
				top: targetScrollTop,
				behavior: 'smooth'
			});

			// Verify scroll completion
			const checkScrollComplete = () => {
				const currentScrollTop = scrollContainer.scrollTop;
				const tolerance = 8;
				const isAtTargetPosition = Math.abs(currentScrollTop - targetScrollTop) <= tolerance;
				const isAtMaxScroll = Math.abs(currentScrollTop - (scrollHeight - clientHeight)) <= tolerance;
				const atBottom = isAtBottom();

				if ((isAtTargetPosition || isAtMaxScroll || atBottom) || attempt >= 2) {
					// Successfully reached bottom or max attempts
					return;
				} else if (attempt < 2) {
					// Try again with a slight delay
					setTimeout(() => attemptScroll(attempt + 1), 100);
				} else {
					// Final fallback: force instant scroll to absolute bottom
					const finalScrollTop = Math.max(targetScrollTop, scrollHeight - clientHeight);
					scrollContainer.scrollTop = finalScrollTop;
				}
			};

			// Wait for smooth scroll animation to complete
			setTimeout(checkScrollComplete, 300);
		};

		attemptScroll();
	}

	// Update sticky header based on scroll position
	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];
			if (!element) continue;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= 20) { // 20px threshold for sticky header activation
				currentDateGroup = dateGroups[i];
			} else {
				break;
			}
		}

		if (currentDateGroup && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true;
			stickyDate = currentDateGroup.label;
		} else {
			showStickyHeader = false;
		}
	}
</script>

<!-- Main wrapper with relative positioning for floating button -->
<div class="relative h-full flex flex-col overflow-hidden">
	{#if loading}
		<div class="flex h-full items-center justify-center">
			<div class="flex items-center space-x-2">
				<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
				<span class="text-gray-500">Loading email threads...</span>
			</div>
		</div>
	{:else if threads.length === 0}
		<div class="flex h-full items-center justify-center">
			<div class="text-center text-gray-500">
				<MailBoxOutline class="mx-auto h-12 w-12 mb-4 text-gray-400" />
				<p>No email threads found</p>
			</div>
		</div>
	{:else}
		<!-- Scrollable content area -->
		<div class="flex-1 overflow-y-scroll px-4 py-6" bind:this={scrollContainer} on:scroll={handleScroll}>
			<!-- Sticky Header -->
			{#if showStickyHeader}
				<div class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200">
					<div class="my-4 flex items-center justify-center">
						<span class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white">
							{stickyDate}
						</span>
					</div>
				</div>
			{/if}
			<!-- Dynamic date group iteration -->
			{#each dateGroups as dateGroup, dateGroupIndex (dateGroup.dateKey)}
				{#if dateGroup.threads.length > 0}
					<div class="mb-8" data-time-group={dateGroup.label}>
						<div
							class="flex items-center justify-center mb-8"
							bind:this={dateGroupElements[dateGroupIndex]}
						>
							<span class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white">
								{dateGroup.label}
							</span>
						</div>
						{#each dateGroup.threads as thread (thread.id)}
						<div
								class="mb-2 overflow-hidden bg-white border-b border-gray-100 rounded-lg shadow-md"
								data-thread-new={thread.isNew ? 'true' : 'false'}
								data-thread-unread={thread.hasUnread ? 'true' : 'false'}
								data-thread-id={thread.id}
							>
							<!-- Thread header -->
							<div
								class="p-4 cursor-pointer transition-colors duration-150"
								on:click={() => toggleThread(thread.id)}
								on:keydown={(e) => e.key === 'Enter' && toggleThread(thread.id)}
								role="button"
								tabindex="0"
							>
								<div class="flex items-start justify-between">
									<div class="flex items-start space-x-3 flex-1 min-w-0">
										<!-- Expand/collapse icon -->
										<div class="flex-shrink-0 mt-1">
											{#if thread.isExpanded}
												<ChevronDownOutline class="h-4 w-4 text-gray-400" />
											{:else}
												<ChevronRightOutline class="h-4 w-4 text-gray-400" />
											{/if}
										</div>

										<!-- Thread info -->
										<div class="flex-1 min-w-0">
											<div class="flex justify-between items-center space-x-2 mb-1">
												<!-- {#if thread.isNew}
													<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
														NEW
													</span>
												{/if} -->
												<h3 class="text-sm font-medium break-words overflow-wrap-anywhere {thread.hasUnread ? 'text-gray-900' : 'text-gray-500'}">
													{thread.subject}
												</h3>
												<!-- Unread indicator -->
												{#if thread.hasUnread}
													<!-- <div class="flex-shrink-0 ml-2">
														<div class="h-2 w-2 bg-red-500 rounded-full"></div>
													</div> -->
													<span
														class="mt-1 inline-flex h-5 min-w-[20px] items-center justify-center rounded-full
																bg-red-500 px-1.5 text-xs font-bold text-white"
													>
														1
													</span>
												{/if}
											</div>

											<div class="flex justify-between items-center text-xs text-gray-500">
												<div class="w-64 truncate">
													<span>{thread.participants.join(', ')}</span>
												</div>
												<div class="flex items-center space-x-1">
													<span>{thread.messageCount}</span>
													<EnvelopeOutline class="h-4 w-4" />
													<!-- <span>•</span> -->
													<!-- <span>{getRelativeTime(thread.lastActivity)}</span> -->
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- Expanded thread messages -->
							{#if thread.isExpanded}
								<div class="thread-messages border-t border-gray-100">
									{#each thread.messages as message (message.id)}
										<div class="border-b border-gray-100 last:border-b-0">
											<div
												class="p-4 cursor-default"
												on:click={() => handleEmailClick(message)}
												on:keydown={(e) => e.key === 'Enter' && handleEmailClick(message)}
												role="button"
												tabindex="0"
											>
												<div class="flex items-start space-x-3">
													<!-- Avatar -->
													<div class="flex-shrink-0">
														{#if message.sender.avatar}
															<img
																src={message.sender.avatar}
																alt={message.sender.name}
																class="h-8 w-8 rounded-full object-cover"
															/>
														{:else}
															<div class="h-8 w-8 rounded-full {getAvatarColor(message.sender.name)} flex items-center justify-center">
																<span class="text-xs font-medium text-white">
																	{getInitials(message.sender.name)}
																</span>
															</div>
														{/if}
													</div>

													<!-- Message content -->
													<div class="flex-1 min-w-0">
														<div class="flex items-center justify-between mb-1">
															<div class="flex items-center space-x-2">
																<span class="text-sm font-medium text-gray-900">
																	{message.sender.name}
																</span>
																{#if !message.isRead}
																	<div class="h-1.5 w-1.5 bg-red-500 rounded-full"></div>
																{/if}
															</div>
															<div class="flex items-center text-xs text-gray-600">
																<!-- Email action buttons -->
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('reply', message, e)}
																	title="ตอบกลับ"
																>
																	<ReplyOutline class="h-4 w-4" />
																</button>
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('replyAll', message, e)}
																	title="ตอบทั้งหมด"
																>
																	<ReplyAllOutline class="h-4 w-4" />
																</button>
																<button
																	class="hover:bg-gray-100 rounded-full p-2"
																	on:click={(e) => handleEmailAction('forward', message, e)}
																	title="ส่งต่อ"
																>
																	<ForwardOutline class="h-4 w-4" />
																</button>
																<div class="flex items-center space-x-2 ml-2">
																	<ClockOutline class="h-4 w-4" />
																	<span>{formatMessageTime(message.timestamp)}</span>
																</div>
															</div>
														</div>

														<p class="text-sm text-gray-600 mb-3 overflow-hidden break-words">
															{@html formatEmailBodyReactive(truncateMessage(message.body, 150))}
															<!-- {@html formatEmailBodyReactive(message.body, message.id)} -->
														</p>

														<!-- Show quoted content toggle button if message has quoted content -->
														{#if hasQuotedContent(message.body)}
															<button
																class="text-xs text-blue-600 hover:text-blue-800 mb-3 flex items-center space-x-1"
																on:click={() => toggleQuotedContent(message.id)}
															>
																{#if expandedQuotedContent[message.id]}
																	<ChevronDownOutline class="h-3 w-3" />
																	<span>Hide quoted text</span>
																{:else}
																	<ChevronRightOutline class="h-3 w-3" />
																	<span>Show quoted text</span>
																{/if}
															</button>
														{/if}

														{#if message.hasAttachments}
															<div class="flex items-center space-x-1 text-xs text-gray-500 mb-3">
																<svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
																</svg>
																<span>{message.attachmentCount} เอกสารแนบ</span>
															</div>
														{/if}
													</div>
												</div>
											</div>
										</div>
									{/each}
								</div>
							{/if}
						</div>
					{/each}
				</div>
			{/if}
		{/each}
	</div>

	<!-- Floating Scroll to Bottom Button -->
	{#if showScrollToBottomButton}
		<button
			id="email-thread-scroll-to-bottom-button"
			class="scroll-to-bottom-button z-20 flex h-8 w-8 items-center justify-center rounded bg-gray-900 bg-opacity-80 text-white shadow-lg transition-all duration-300 hover:bg-opacity-100 hover:shadow-xl focus:outline-none"
			on:click={handleScrollToBottomClick}
			aria-label="เลื่อนไปด้านล่าง"
			title="เลื่อนไปด้านล่าง"
		>
			<ArrowDownOutline class="h-6 w-6" />
		</button>
	{/if}

	<!-- Floating Action Button (FAB) for Compose Email -->
	<button
		class="absolute bottom-3 right-6 w-12 h-12 bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white rounded-full shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-200 ease-out z-20 flex items-center justify-center group animate-fade-in-up hover:scale-105 active:scale-95 transform will-change-transform"
		on:click={() => startNewComposition()}
		title="เขียนอีเมลใหม่"
		aria-label="เขียนอีเมลใหม่"
	>
		<PenOutline class="w-6 h-6 md:w-5 md:h-5 transition-transform group-hover:scale-110" />
	</button>
	{/if}
</div>


<!-- Email Composition Modal -->
<EmailCompositionModal
	bind:isOpen={isCompositionModalOpen}
	compositionData={currentCompositionData}
	on:send={handleModalSend}
	on:cancel={handleModalCancel}
	on:close={handleModalCancel}
/>
<style>
	/* Custom scrollbar styling for better UX */
	:global(.overflow-y-auto) {
		scrollbar-width: thin;
		scrollbar-color: #cbd5e0 #f7fafc;
	}

	:global(.overflow-y-auto::-webkit-scrollbar) {
		width: 6px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-track) {
		background: #f7fafc;
		border-radius: 3px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-thumb) {
		background: #cbd5e0;
		border-radius: 3px;
	}

	:global(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
		background: #a0aec0;
	}

	/* Ensure proper text wrapping for long content */
	:global(.break-words) {
		word-break: break-word;
		overflow-wrap: break-word;
		hyphens: auto;
	}

	:global(.overflow-wrap-anywhere) {
		overflow-wrap: anywhere;
	}

	/* Scroll to bottom button positioning and animations */
	.scroll-to-bottom-button {
		animation: fadeInUp 0.3s ease-out;
		/* Position relative to the wrapper container's visible area */
		position: absolute;
		bottom: 4.5rem; /* Position above the compose FAB */
		right: 2.0rem;
		/* Ensure it stays in place during scroll */
		transform: translateZ(0);
		will-change: transform;
	}

	/* Custom animation for FAB appearance */
	.animate-fade-in-up {
		animation: fadeInUp 0.3s ease-out;
	}

	/* Animation for FAB appearance (same as MessageList.svelte) */
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(10px) translateZ(0);
		}
		to {
			opacity: 1;
			transform: translateY(0) translateZ(0);
		}
	}
</style>
