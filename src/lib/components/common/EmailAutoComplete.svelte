<script lang="ts">
	import { createEventDispatcher, onMount, onD<PERSON>roy, tick } from 'svelte';
	import { UserOutline, CloseOutline } from 'flowbite-svelte-icons';

	// Types (previously from contactService)
	interface EmailContact {
		id: string;
		email: string;
		name?: string;
		displayName: string; // Either name or email if name is not available
		frequency: number; // How often this contact appears in threads
		lastSeen: Date;
		source: 'thread' | 'manual'; // How the contact was added
		avatar?: string;
	}

	interface ContactSearchResult {
		contact: EmailContact;
		score: number; // Relevance score for search results
	}

	// Mock contact data - this will eventually be replaced with backend API calls
	const mockContacts: EmailContact[] = [
		{
			id: 'contact_1',
			email: '<EMAIL>',
			name: '<PERSON>',
			displayName: '<PERSON>',
			frequency: 15,
			lastSeen: new Date('2024-01-15'),
			source: 'thread'
		},
		{
			id: 'contact_2',
			email: '<EMAIL>',
			name: '<PERSON>',
			displayName: '<PERSON>',
			frequency: 8,
			lastSeen: new Date('2024-01-10'),
			source: 'thread'
		},
		{
			id: 'contact_3',
			email: '<EMAIL>',
			name: '<PERSON>',
			displayName: 'Bob <PERSON>',
			frequency: 12,
			lastSeen: new Date('2024-01-12'),
			source: 'thread'
		},
		{
			id: 'contact_4',
			email: '<EMAIL>',
			name: 'Alice <PERSON>',
			displayName: '<PERSON>',
			frequency: 20,
			lastSeen: new Date('2024-01-18'),
			source: 'thread'
		},
		{
			id: 'contact_5',
			email: '<EMAIL>',
			name: 'Mike Brown',
			displayName: 'Mike Brown',
			frequency: 5,
			lastSeen: new Date('2024-01-08'),
			source: 'thread'
		},
		{
			id: 'contact_6',
			email: '<EMAIL>',
			name: 'Sarah Davis',
			displayName: 'Sarah Davis',
			frequency: 18,
			lastSeen: new Date('2024-01-16'),
			source: 'thread'
		},
		{
			id: 'contact_7',
			email: '<EMAIL>',
			name: 'Tom Anderson',
			displayName: 'Tom Anderson',
			frequency: 3,
			lastSeen: new Date('2024-01-05'),
			source: 'manual'
		},
		{
			id: 'contact_8',
			email: '<EMAIL>',
			name: 'Lisa Martinez',
			displayName: 'Lisa Martinez',
			frequency: 10,
			lastSeen: new Date('2024-01-14'),
			source: 'thread'
		},
		// Company contacts from mock email data
		{
			id: 'contact_9',
			email: '<EMAIL>',
			name: 'Samuel Jennings',
			displayName: 'Samuel Jennings',
			frequency: 25,
			lastSeen: new Date('2024-01-20'),
			source: 'thread'
		},
		{
			id: 'contact_10',
			email: '<EMAIL>',
			name: 'IT Team',
			displayName: 'IT Team',
			frequency: 18,
			lastSeen: new Date('2024-01-19'),
			source: 'thread'
		},
		{
			id: 'contact_11',
			email: '<EMAIL>',
			name: 'Sales Manager',
			displayName: 'Sales Manager',
			frequency: 22,
			lastSeen: new Date('2024-01-18'),
			source: 'thread'
		},
		{
			id: 'contact_12',
			email: '<EMAIL>',
			name: 'Department Manager',
			displayName: 'Department Manager',
			frequency: 15,
			lastSeen: new Date('2024-01-17'),
			source: 'thread'
		},
		{
			id: 'contact_13',
			email: '<EMAIL>',
			name: 'IT Support Team',
			displayName: 'IT Support Team',
			frequency: 20,
			lastSeen: new Date('2024-01-16'),
			source: 'thread'
		},
		{
			id: 'contact_14',
			email: '<EMAIL>',
			name: 'Security Team',
			displayName: 'Security Team',
			frequency: 12,
			lastSeen: new Date('2024-01-15'),
			source: 'thread'
		},
		// Sarah Mitchell - Featured contact from email threads
		{
			id: 'contact_15',
			email: '<EMAIL>',
			name: 'Sarah Mitchell',
			displayName: 'Sarah Mitchell',
			frequency: 35,
			lastSeen: new Date('2025-09-25'),
			source: 'thread'
		},
		{
			id: 'contact_15',
			email: '<EMAIL>',
			name: 'Project Manager',
			displayName: 'Project Manager',
			frequency: 16,
			lastSeen: new Date('2024-01-14'),
			source: 'thread'
		},
		{
			id: 'contact_16',
			email: '<EMAIL>',
			name: 'Sales Team',
			displayName: 'Sales Team',
			frequency: 30,
			lastSeen: new Date('2024-01-21'),
			source: 'thread'
		},
		{
			id: 'contact_17',
			email: '<EMAIL>',
			name: 'Training Department',
			displayName: 'Training Department',
			frequency: 8,
			lastSeen: new Date('2024-01-13'),
			source: 'thread'
		},
		{
			id: 'contact_18',
			email: '<EMAIL>',
			name: 'CEO',
			displayName: 'CEO',
			frequency: 5,
			lastSeen: new Date('2024-01-12'),
			source: 'thread'
		},
		{
			id: 'contact_19',
			email: '<EMAIL>',
			name: 'Finance Team',
			displayName: 'Finance Team',
			frequency: 14,
			lastSeen: new Date('2024-01-11'),
			source: 'thread'
		},
		{
			id: 'contact_20',
			email: '<EMAIL>',
			name: 'HR Department',
			displayName: 'HR Department',
			frequency: 11,
			lastSeen: new Date('2024-01-10'),
			source: 'thread'
		},
		{
			id: 'contact_21',
			email: '<EMAIL>',
			name: 'Operations Team',
			displayName: 'Operations Team',
			frequency: 13,
			lastSeen: new Date('2024-01-09'),
			source: 'thread'
		},
		{
			id: 'contact_22',
			email: '<EMAIL>',
			name: 'CFO',
			displayName: 'CFO',
			frequency: 7,
			lastSeen: new Date('2024-01-08'),
			source: 'thread'
		},
		{
			id: 'contact_23',
			email: '<EMAIL>',
			name: 'Sarah Chen',
			displayName: 'Sarah Chen',
			frequency: 19,
			lastSeen: new Date('2024-01-07'),
			source: 'thread'
		},
		{
			id: 'contact_24',
			email: '<EMAIL>',
			name: 'Marketing Team',
			displayName: 'Marketing Team',
			frequency: 17,
			lastSeen: new Date('2024-01-06'),
			source: 'thread'
		},
		{
			id: 'contact_25',
			email: '<EMAIL>',
			name: 'Mike Rodriguez',
			displayName: 'Mike Rodriguez',
			frequency: 21,
			lastSeen: new Date('2024-01-05'),
			source: 'thread'
		},
		{
			id: 'contact_26',
			email: '<EMAIL>',
			name: 'Warehouse Team',
			displayName: 'Warehouse Team',
			frequency: 9,
			lastSeen: new Date('2024-01-04'),
			source: 'thread'
		},
		{
			id: 'contact_27',
			email: '<EMAIL>',
			name: 'Procurement Department',
			displayName: 'Procurement Department',
			frequency: 6,
			lastSeen: new Date('2024-01-03'),
			source: 'thread'
		}
	];

	// Mock contact service functions
	const mockContactService = {
		searchContacts: (query: string, limit: number = 10): ContactSearchResult[] => {
			if (!query.trim()) {
				return [];
			}

			const normalizedQuery = query.toLowerCase().trim();
			const results: ContactSearchResult[] = [];

			mockContacts.forEach(contact => {
				const score = calculateSearchScore(contact, normalizedQuery);
				if (score > 0) {
					results.push({ contact, score });
				}
			});

			// Sort by score (descending) and frequency (descending)
			results.sort((a, b) => {
				if (a.score !== b.score) {
					return b.score - a.score;
				}
				return b.contact.frequency - a.contact.frequency;
			});

			return results.slice(0, limit);
		},

		getContact: (email: string): EmailContact | undefined => {
			return mockContacts.find(contact => contact.email.toLowerCase() === email.toLowerCase());
		},

		addContact: (email: string, name?: string, source: 'thread' | 'manual' = 'manual'): EmailContact => {
			// In a real implementation, this would save to backend
			// For now, just create a temporary contact object
			const contact: EmailContact = {
				id: `temp_${Date.now()}_${Math.random()}`,
				email: email.toLowerCase().trim(),
				name: name,
				displayName: name || email,
				frequency: 1,
				lastSeen: new Date(),
				source
			};
			return contact;
		}
	};

	// Utility functions (previously from emailExtractor)
	function parseEmailList(emailString: string): Array<{email: string, name?: string}> {
		if (!emailString || typeof emailString !== 'string') {
			return [];
		}

		const emails: Array<{email: string, name?: string}> = [];
		const parts = emailString.split(',');

		parts.forEach(part => {
			const parsed = parseParticipantString(part);
			if (parsed) {
				emails.push(parsed);
			}
		});

		return emails;
	}

	function parseParticipantString(participant: string): {email: string, name?: string} | null {
		if (!participant || typeof participant !== 'string') {
			return null;
		}

		const trimmed = participant.trim();

		// Check for "Name <email>" format
		const nameEmailMatch = trimmed.match(/^(.+?)\s*<([^>]+)>$/);
		if (nameEmailMatch) {
			const name = nameEmailMatch[1].trim();
			const email = nameEmailMatch[2].trim();
			if (isValidEmail(email)) {
				return { email, name: name || undefined };
			}
		}

		// Check if it's just an email
		if (isValidEmail(trimmed)) {
			return { email: trimmed };
		}

		// Check if it's a name that might contain an email
		const emailInNameMatch = trimmed.match(/([^\s@]+@[^\s@]+\.[^\s@]+)/);
		if (emailInNameMatch) {
			const email = emailInNameMatch[1];
			if (isValidEmail(email)) {
				const name = trimmed.replace(email, '').trim().replace(/[<>]/g, '').trim();
				return { email, name: name || undefined };
			}
		}

		return null;
	}

	function formatContactForDisplay(contact: EmailContact): string {
		if (contact.name && contact.name !== contact.email) {
			return `${contact.name} <${contact.email}>`;
		}
		return contact.email;
	}

	function isValidEmail(email: string): boolean {
		if (!email || typeof email !== 'string') {
			return false;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email.trim());
	}

	function calculateSearchScore(contact: EmailContact, query: string): number {
		let score = 0;

		// Exact email match gets highest score
		if (contact.email === query) {
			score += 100;
		}
		// Email starts with query
		else if (contact.email.startsWith(query)) {
			score += 80;
		}
		// Email contains query
		else if (contact.email.includes(query)) {
			score += 60;
		}

		// Name matching (if available)
		if (contact.name) {
			const normalizedName = contact.name.toLowerCase();
			if (normalizedName === query) {
				score += 90;
			} else if (normalizedName.startsWith(query)) {
				score += 70;
			} else if (normalizedName.includes(query)) {
				score += 50;
			}
		}

		// Boost score based on frequency (up to 20 points)
		score += Math.min(contact.frequency * 2, 20);

		// Boost score for recent contacts (up to 10 points)
		const daysSinceLastSeen = (Date.now() - contact.lastSeen.getTime()) / (1000 * 60 * 60 * 24);
		if (daysSinceLastSeen < 7) {
			score += 10 - Math.floor(daysSinceLastSeen);
		}

		return score;
	}

	// Props
	export let value: string = '';
	export let placeholder: string = 'Enter email addresses...';
	export let disabled: boolean = false;
	export let multiple: boolean = true;
	export let maxSuggestions: number = 8;
	export let minQueryLength: number = 1;
	export let id: string = '';
	export let name: string = '';

	// Component state
	let inputElement: HTMLInputElement;
	let dropdownElement: HTMLDivElement;
	let currentQuery: string = '';
	let suggestions: ContactSearchResult[] = [];
	let selectedIndex: number = -1;
	let showDropdown: boolean = false;
	let selectedContacts: EmailContact[] = [];
	let inputValue: string = '';

	const dispatch = createEventDispatcher<{
		change: { value: string; contacts: EmailContact[] };
		input: { value: string };
		focus: void;
		blur: void;
	}>();

	// Initialize component
	onMount(() => {
		parseInitialValue();
		document.addEventListener('click', handleOutsideClick);
		window.addEventListener('resize', handleWindowResize);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleOutsideClick);
		window.removeEventListener('resize', handleWindowResize);
	});

	// Parse initial value into selected contacts
	function parseInitialValue() {
		if (value) {
			const parsed = parseEmailList(value);
			selectedContacts = parsed.map(p => {
				const existing = mockContactService.getContact(p.email);
				return existing || {
					id: `temp_${Date.now()}_${Math.random()}`,
					email: p.email,
					name: p.name,
					displayName: p.name || p.email,
					frequency: 0,
					lastSeen: new Date(),
					source: 'manual' as const
				};
			});
		}
	}

	// Handle input changes
	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		inputValue = target.value;
		currentQuery = inputValue.trim();

		dispatch('input', { value: inputValue });

		if (currentQuery.length >= minQueryLength) {
			searchContacts();
		} else {
			hideSuggestions();
		}
	}

	// Search for contacts
	async function searchContacts() {
		if (!currentQuery) {
			hideSuggestions();
			return;
		}

		suggestions = mockContactService.searchContacts(currentQuery, maxSuggestions);

		// Filter out already selected contacts
		const selectedEmails = new Set(selectedContacts.map(c => c.email.toLowerCase()));
		suggestions = suggestions.filter(s => !selectedEmails.has(s.contact.email.toLowerCase()));

		selectedIndex = -1;
		showDropdown = suggestions.length > 0;

		if (showDropdown) {
			await tick();
			positionDropdown();
		}
	}

	// Position dropdown relative to input
	function positionDropdown() {
		if (!dropdownElement || !inputElement) return;

		// Check if dropdown would go off-screen and adjust positioning
		const inputRect = inputElement.getBoundingClientRect();
		const viewportHeight = window.innerHeight;

		// Estimate dropdown height (max-h-60 = 240px)
		const estimatedDropdownHeight = Math.min(suggestions.length * 48 + 16, 240);

		// Check if there's enough space below
		const spaceBelow = viewportHeight - inputRect.bottom;
		const spaceAbove = inputRect.top;

		// Remove any previous positioning classes
		dropdownElement.classList.remove('top-full', 'bottom-full');

		if (spaceBelow >= estimatedDropdownHeight || spaceBelow >= spaceAbove) {
			// Show below (default)
			dropdownElement.classList.add('top-full');
		} else {
			// Show above
			dropdownElement.classList.add('bottom-full');
		}
	}

	// Hide suggestions
	function hideSuggestions() {
		showDropdown = false;
		selectedIndex = -1;
	}

	// Scroll selected item into view
	function scrollSelectedItemIntoView() {
		if (!dropdownElement || selectedIndex < 0) return;

		// Get the selected item element using data attribute
		const selectedItem = dropdownElement.querySelector(`[data-suggestion-index="${selectedIndex}"]`) as HTMLElement;
		if (!selectedItem) return;

		// Get the dropdown container's scroll position and dimensions
		const dropdownRect = dropdownElement.getBoundingClientRect();
		const itemRect = selectedItem.getBoundingClientRect();

		// Calculate if the item is outside the visible area
		const itemTop = itemRect.top - dropdownRect.top + dropdownElement.scrollTop;
		const itemBottom = itemTop + itemRect.height;
		const visibleTop = dropdownElement.scrollTop;
		const visibleBottom = visibleTop + dropdownElement.clientHeight;

		// Scroll if the item is not fully visible
		if (itemTop < visibleTop) {
			// Item is above the visible area - scroll up
			dropdownElement.scrollTop = itemTop;
		} else if (itemBottom > visibleBottom) {
			// Item is below the visible area - scroll down
			dropdownElement.scrollTop = itemBottom - dropdownElement.clientHeight;
		}
	}

	// Handle keyboard navigation
	function handleKeyDown(event: KeyboardEvent) {
		// Handle backspace to remove last chip when input is empty
		if (event.key === 'Backspace' && !inputValue && multiple && selectedContacts.length > 0) {
			event.preventDefault();
			removeContact(selectedContacts.length - 1);
			return;
		}

		if (!showDropdown) {
			if (event.key === 'ArrowDown') {
				event.preventDefault();
				searchContacts();
			}
			return;
		}

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
				scrollSelectedItemIntoView();
				break;
			case 'ArrowUp':
				event.preventDefault();
				selectedIndex = Math.max(selectedIndex - 1, -1);
				scrollSelectedItemIntoView();
				break;
			case 'Enter':
				event.preventDefault();
				if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
					selectContact(suggestions[selectedIndex].contact);
				} else if (currentQuery && isValidEmail(currentQuery)) {
					addManualContact(currentQuery);
				}
				break;
			case 'Escape':
				event.preventDefault();
				hideSuggestions();
				break;
			case 'Tab':
				if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
					event.preventDefault();
					selectContact(suggestions[selectedIndex].contact);
				}
				break;
		}
	}

	// Select a contact from suggestions
	function selectContact(contact: EmailContact) {
		if (multiple) {
			selectedContacts = [...selectedContacts, contact];
		} else {
			selectedContacts = [contact];
		}
		
		inputValue = '';
		currentQuery = '';
		hideSuggestions();
		updateValue();
		
		// Focus back to input for multiple selection
		if (multiple) {
			inputElement?.focus();
		}
	}

	// Add manual contact (typed email)
	function addManualContact(email: string) {
		if (!isValidEmail(email)) return;

		const contact: EmailContact = {
			id: `manual_${Date.now()}_${Math.random()}`,
			email: email.toLowerCase().trim(),
			displayName: email,
			frequency: 0,
			lastSeen: new Date(),
			source: 'manual'
		};

		// Add to contact service (mock - in real implementation this would save to backend)
		mockContactService.addContact(contact.email, undefined, 'manual');

		if (multiple) {
			selectedContacts = [...selectedContacts, contact];
		} else {
			selectedContacts = [contact];
		}

		inputValue = '';
		currentQuery = '';
		hideSuggestions();
		updateValue();

		if (multiple) {
			inputElement?.focus();
		}
	}

	// Remove selected contact
	function removeContact(index: number) {
		selectedContacts = selectedContacts.filter((_, i) => i !== index);
		updateValue();
		inputElement?.focus();
	}

	// Update the component value
	function updateValue() {
		const newValue = selectedContacts.map(formatContactForDisplay).join(', ');
		value = newValue;
		dispatch('change', { value: newValue, contacts: selectedContacts });
	}

	// Handle outside clicks
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as Element;
		if (!inputElement?.contains(target) && !dropdownElement?.contains(target)) {
			hideSuggestions();
		}
	}

	// Handle window resize
	function handleWindowResize() {
		if (showDropdown) {
			// Hide dropdown on resize to avoid positioning issues
			hideSuggestions();
		}
	}

	// Handle focus
	function handleFocus() {
		dispatch('focus');
		if (currentQuery.length >= minQueryLength) {
			searchContacts();
		}
	}

	// Handle blur
	function handleBlur() {
		// Delay to allow for dropdown clicks
		setTimeout(() => {
			dispatch('blur');
		}, 150);
	}

	// Get contact initials for avatar
	function getContactInitials(contact: EmailContact): string {
		if (contact.name) {
			return contact.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
		}
		return contact.email.slice(0, 2).toUpperCase();
	}

	// Get avatar color
	function getAvatarColor(contact: EmailContact): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];
		
		let hash = 0;
		const str = contact.email;
		for (let i = 0; i < str.length; i++) {
			hash = str.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}
</script>

<div class="relative">
	<!-- Input field with inline chips -->
	<div class="relative">
		<!-- Input container that looks like a single input field -->
		<div
			class="w-full min-h-[2.5rem] px-3 py-2 text-sm border-0 focus-within:ring-0 bg-white flex flex-wrap items-center gap-1 {disabled ? 'bg-gray-100 cursor-not-allowed' : ''}"
			role="textbox"
			tabindex="-1"
			on:click={() => !disabled && inputElement?.focus()}
			on:keydown={(e) => e.key === 'Enter' && !disabled && inputElement?.focus()}
		>
			<!-- Selected contact chips (inline) -->
			{#if multiple && selectedContacts.length > 0}
				{#each selectedContacts as contact, index (contact.id)}
					<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 flex-shrink-0">
						<!-- <div class="w-3 h-3 rounded-full {getAvatarColor(contact)} flex items-center justify-center text-white text-[10px] mr-1">
							{getContactInitials(contact)}
						</div> -->
						<span class="max-w-[120px] truncate">{contact.displayName}</span>
						<button
							type="button"
							class="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full hover:bg-blue-200 focus:outline-none"
							on:click|stopPropagation={() => removeContact(index)}
						>
							<CloseOutline class="w-2 h-2" />
						</button>
					</span>
				{/each}
			{/if}

			<!-- Actual input field -->
			<input
				bind:this={inputElement}
				bind:value={inputValue}
				on:input={handleInput}
				on:keydown={handleKeyDown}
				on:focus={handleFocus}
				on:blur={handleBlur}
				{id}
				{name}
				placeholder={multiple && selectedContacts.length > 0 ? '' : placeholder}
				{disabled}
				class="flex-1 min-w-[120px] border-none outline-none bg-transparent text-sm {disabled ? 'cursor-not-allowed' : ''}"
				autocomplete="off"
				spellcheck="false"
			/>
		</div>

		{#if !multiple && selectedContacts.length > 0}
			<button
				type="button"
				class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
				on:click={() => { selectedContacts = []; updateValue(); }}
			>
				<CloseOutline class="w-4 h-4" />
			</button>
		{/if}

		<!-- Dropdown suggestions -->
		{#if showDropdown}
			<div
				bind:this={dropdownElement}
				class="email-autocomplete-dropdown absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto left-0 right-0 top-full mt-1"
			>
			{#each suggestions as suggestion, index (suggestion.contact.id)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none {selectedIndex === index ? 'bg-blue-50' : ''}"
					data-suggestion-index={index}
					on:click={() => selectContact(suggestion.contact)}
				>
					<div class="flex items-center space-x-2">
						<div class="w-6 h-6 rounded-full {getAvatarColor(suggestion.contact)} flex items-center justify-center text-white text-xs">
							{getContactInitials(suggestion.contact)}
						</div>
						<div class="flex-1 min-w-0">
							<div class="text-sm font-medium text-gray-900 truncate">
								{suggestion.contact.displayName}
							</div>
							{#if suggestion.contact.name && suggestion.contact.name !== suggestion.contact.email}
								<div class="text-xs text-gray-500 truncate">
									{suggestion.contact.email}
								</div>
							{/if}
						</div>
						{#if suggestion.contact.frequency > 1}
							<div class="text-xs text-gray-400">
								{suggestion.contact.frequency}x
							</div>
						{/if}
					</div>
				</button>
			{/each}
			
			{#if suggestions.length === 0 && currentQuery && isValidEmail(currentQuery)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
					on:click={() => addManualContact(currentQuery)}
				>
					<div class="flex items-center space-x-2">
						<UserOutline class="w-6 h-6 text-gray-400" />
						<div class="flex-1">
							<div class="text-sm text-gray-900">
								Add "{currentQuery}"
							</div>
							<div class="text-xs text-gray-500">
								Press Enter to add this email
							</div>
						</div>
					</div>
				</button>
			{/if}
		</div>
	{/if}
	</div>
</div>

<style>
	/* Custom styles for dropdown positioning */
	:global(.email-autocomplete-dropdown.bottom-full) {
		top: auto !important;
		bottom: 100% !important;
		margin-top: 0 !important;
		margin-bottom: 0.25rem !important;
	}
</style>
